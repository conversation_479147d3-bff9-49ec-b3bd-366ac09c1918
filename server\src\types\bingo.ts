export type BingoColumn = 'B' | 'I' | 'N' | 'G' | 'O';

export interface BingoColumnRanges {
  B: { min: 1; max: 15 };
  I: { min: 16; max: 30 };
  N: { min: 31; max: 45 };
  G: { min: 46; max: 60 };
  O: { min: 61; max: 75 };
}

export interface BingoCellData {
  id: string;           // Simple ID like "B1", "I2", "N3", etc.
  column: BingoColumn;
  number: number;
  position: number;     // 0-24 for 5x5 grid (0=top-left, 24=bottom-right)
  isFree: boolean;
  isMarked: boolean;
}

export interface CalledNumber {
  column: BingoColumn;
  number: number;
  callOrder: number;
  timestamp: number;
}

export type WinningPattern = 'horizontal' | 'vertical' | 'diagonal' | 'fullCard';

export interface WinResult {
  hasWon: boolean;
  pattern?: WinningPattern;
  winningCells?: BingoCellData[];
}

export interface BingoGameData {
  roomId: string;
  bingoCard: BingoCellData[][];
  calledNumbers: CalledNumber[];
  availableNumbers: number[];
  currentCallOrder: number;
  gameStartTime: number;
  lastCallTime: number;
  serverSeed: string;
  clientSeed: string;
  nonce: number;
  isGameActive: boolean;
}

export interface BingoGameState {
  bingoCard: BingoCellData[][];
  calledNumbers: CalledNumber[];
  currentCallOrder: number;
  gameStartTime: number;
  lastCallTime: number;
  isGameActive: boolean;
}

export interface CellMarkResult {
  success: boolean;
  isCorrect: boolean;
  points: number;
  newScore: number;
  newLives: number;
  gameEnded: boolean;
  winResult?: WinResult;
  gameState?: BingoGameState;
  matchedCalledNumber?: CalledNumber;
}

export interface NumberCallResult {
  calledNumber: CalledNumber;
  gameState: BingoGameState;
}

export const BINGO_CONSTANTS = {
  GAME_DURATION: 60, // in seconds
  GRID_SIZE: 5,
  TOTAL_NUMBERS: 75,
  INITIAL_CALLOUT_DELAY: 500,
  CALLOUT_DELAY: 1800,
  FREE_SPACE_POSITION: { row: 2, col: 2 },
  COLUMNS: ['B', 'I', 'N', 'G', 'O'] as const,
  COLUMN_RANGES: {
    B: { min: 1, max: 15 },
    I: { min: 16, max: 30 },
    N: { min: 31, max: 45 },
    G: { min: 46, max: 60 },
    O: { min: 61, max: 75 }
  } as BingoColumnRanges,
  SCORING: {
    MAX_SCORE: 100,    // Maximum score for immediate clicks
    MIN_SCORE: 10,     // Minimum score regardless of timing
    TIMER_DURATION: 8500, // 8.5 seconds in milliseconds (matches client)
    WRONG_MARK_PENALTY: 0, // No penalty, just lose a life
    PATTERN_BONUS: {
      horizontal: 500,
      vertical: 500,
      diagonal: 500,
      fullCard: 1000
    }
  }
} as const;
