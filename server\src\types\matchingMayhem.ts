/**
 * Matching Mayhem Game Types and Interfaces
 * Server-side types for the Matching Mayhem game
 */

// Animal and Color Types
export type AnimalName = 'eagle' | 'koala' | 'wolf' | 'dog';
export type ColorName = 'cyan' | 'green' | 'yellow';

// Card data structure
export interface CardData {
  animalIndex: number;
  colorIndex: number;
  imageKey: string;
  isCorrect: boolean;
  position: number; // 0-4 for the 5 card positions
}

// Round data structure
export interface RoundData {
  roundNumber: number;
  correctCardIndex: number; // Which position (0-4) has the correct card
  mainCard: {
    animalIndex: number;
    colorIndex: number;
    imageKey: string;
  };
  cards: CardData[];
  startTime: number;
  timeLimit: number; // in milliseconds
}

// Game state specific to Matching Mayhem
export interface MatchingMayhemGameState {
  currentRound: RoundData | null;
  roundsCompleted: number;
  isRoundActive: boolean;
  roundStartTime: number | null;
}

// Action types for Matching Mayhem
export type MatchingMayhemActionType = 'card_select';

// Card selection action data
export interface CardSelectActionData {
  roomId: string;
  gameId: string;
  action: {
    type: 'card_select';
    data: {
      cardIndex: number; // 0-4 for which card was selected
      reactionTime?: number; // time taken to select the card
      clickTime: number; // timestamp when card was clicked
    };
  };
}

// Action result for card selection
export interface CardSelectResult {
  success: boolean;
  isCorrect: boolean;
  points: number;
  newScore: number;
  newLives: number;
  gameEnded: boolean;
  nextRound?: RoundData;
  correctCardIndex: number;
}

// Socket event data structures specific to Matching Mayhem
export interface MatchingMayhemStartData {
  roomId: string;
  gameId: string;
}

export interface MatchingMayhemGameStartedData {
  gameState: {
    score: number;
    lives: number;
    isActive: boolean;
    startTime?: number;
  };
  firstRound: RoundData;
  message: string;
}

export interface MatchingMayhemActionResultData {
  actionType: 'card_select';
  data: {
    cardIndex: number;
    isCorrect: boolean;
    points: number;
    newScore: number;
    newLives: number;
    gameEnded: boolean;
    nextRound?: RoundData;
    correctCardIndex: number;
  };
}

// Constants for Matching Mayhem
export const MATCHING_MAYHEM_CONSTANTS = {
  GAME_DURATION: 20, // 20 seconds total game time
  ROUND_TIME: 5, // 5 seconds per round
  CARD_COUNT: 5, // 5 cards per round
  ANIMAL_COUNT: 4, // 4 different animals
  COLOR_COUNT: 3, // 3 different colors
  CENTER_CARD_INDEX: 2, // Center card is always the matching distractor
  
  SCORING: {
    MAX_ROUND_SCORE: 100, // Maximum points for immediate selection
    MIN_ROUND_SCORE: 10,  // Minimum points regardless of timing
    WRONG_ANSWER_PENALTY: 20, // Points deducted for wrong answer
    TIME_BONUS_MULTIPLIER: 1.0 // Multiplier for time-based bonus
  },
  
  LIVES: {
    INITIAL_LIVES: 3,
    DEDUCT_ON_WRONG: 1
  }
} as const;

// Animal and color definitions (matching client-side)
export const ANIMAL_NAMES: readonly AnimalName[] = ['eagle', 'koala', 'wolf', 'dog'] as const;
export const COLOR_NAMES: readonly ColorName[] = ['cyan', 'green', 'yellow'] as const;

// Category tints for visual effects (matching client-side)
export const CATEGORY_TINTS: Record<number, number> = {
  0: 0x66ffff, // Cyan
  1: 0x66ff66, // Green
  2: 0xffff66  // Yellow
};
