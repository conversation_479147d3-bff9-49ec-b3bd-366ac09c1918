
import type { GameService } from '../../services/gameService';
import { Server, Socket } from 'socket.io';
import { logger } from '../../utils/logger';
import { GAME_TYPES } from '../../utils/constants';

import type {
  GameInitResult,
  EndReason,
  GameStartData,
  GameEndData,
  GameActionData
} from '../../types/game';

import type {
  BingoColumn,
  BingoCellData,
  CalledNumber,
  WinResult,
  BingoGameData,
  BingoGameState,
  CellMarkResult,
  NumberCallResult,
  WinningPattern
} from '../../types/bingo';

import { BINGO_CONSTANTS } from '../../types/bingo';
import { generateFloats } from '../../utils/prngService';

export default class BingoController {
  private gameService: GameService;
  private gameData: Map<string, BingoGameData> = new Map();
  private numberCallTimers: Map<string, NodeJS.Timeout> = new Map();

  constructor(gameService: GameService) {
    this.gameService = gameService;
  }

  /**
   * Generate random floats using PRNG service
   */
  private generateRandomFloats(serverSeed: string, clientSeed: string, nonce: number, count: number): number[] {
    return generateFloats(serverSeed, clientSeed, nonce, 0, count);
  }

  /**
   * Shuffle array using PRNG-generated random values
   */
  private shuffleArray<T>(array: T[], randomValues: number[]): T[] {
    const shuffled = [...array];
    let randomIndex = 0;

    for (let i = shuffled.length - 1; i > 0; i--) {
      // Use the next random value, cycling if needed
      const randomValue = randomValues[randomIndex % randomValues.length];
      randomIndex++;

      const j = Math.floor(randomValue * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  /**
   * Generate a bingo card with PRNG-based randomization using simplified IDs
   */
  private generateBingoCard(serverSeed: string, clientSeed: string, nonce: number): { card: BingoCellData[][], availableNumbers: number[] } {
    // Generate enough random values for card generation and shuffling
    const randomValues = this.generateRandomFloats(serverSeed, clientSeed, nonce, 100);
    let randomIndex = 0;

    const card: BingoCellData[][] = [];
    const usedNumbers = new Set<number>();
    const availableNumbers: number[] = [];

    // Initialize 5x5 grid
    for (let row = 0; row < 5; row++) {
      card[row] = [];
      for (let col = 0; col < 5; col++) {
        const column = BINGO_CONSTANTS.COLUMNS[col];
        const position = row * 5 + col; // 0-24 position

        // Handle center FREE space (position 12)
        if (position === 12) { // Center position (2,2) = 2*5+2 = 12
          const cellId = `${column}FREE`;
          card[row][col] = {
            id: cellId,
            column,
            number: 0,
            position,
            isFree: true,
            isMarked: true // FREE space is automatically marked
          };
        } else {
          // Generate unique number for this column using PRNG
          const range = BINGO_CONSTANTS.COLUMN_RANGES[column];
          let number: number;
          do {
            const randomValue = randomValues[randomIndex % randomValues.length];
            randomIndex++;
            number = range.min + Math.floor(randomValue * (range.max - range.min + 1));
          } while (usedNumbers.has(number));

          usedNumbers.add(number);
          availableNumbers.push(number);

          const cellId = `${column}${number}`;
          card[row][col] = {
            id: cellId,
            column,
            number,
            position,
            isFree: false,
            isMarked: false
          };
        }
      }
    }

    // Add additional numbers to available pool for calling (balance the game)
    const allNumbers = Array.from({ length: 75 }, (_, i) => i + 1);
    const unusedNumbers = allNumbers.filter(num => !usedNumbers.has(num));

    // Generate more random values for shuffling
    const shuffleRandomValues = this.generateRandomFloats(serverSeed, clientSeed, nonce + 1, 50);
    const additionalNumbers = this.shuffleArray(unusedNumbers, shuffleRandomValues).slice(0, 10);
    availableNumbers.push(...additionalNumbers);

    // Shuffle the available numbers for calling
    const finalShuffleValues = this.generateRandomFloats(serverSeed, clientSeed, nonce + 2, availableNumbers.length);
    const shuffledAvailable = this.shuffleArray(availableNumbers, finalShuffleValues);

    return { card, availableNumbers: shuffledAvailable };
  }

  /**
   * Get column for a given number
   */
  private getColumnForNumber(number: number): BingoColumn {
    if (number >= 1 && number <= 15) return 'B';
    if (number >= 16 && number <= 30) return 'I';
    if (number >= 31 && number <= 45) return 'N';
    if (number >= 46 && number <= 60) return 'G';
    if (number >= 61 && number <= 75) return 'O';
    throw new Error(`Invalid bingo number: ${number}`);
  }

  /**
   * Call the next bingo number
   */
  private callNextNumber(roomId: string): CalledNumber | null {
    const gameData = this.gameData.get(roomId);
    if (!gameData || gameData.availableNumbers.length === 0) {
      return null;
    }

    const number = gameData.availableNumbers.shift()!;
    const column = this.getColumnForNumber(number);

    const calledNumber: CalledNumber = {
      column,
      number,
      callOrder: ++gameData.currentCallOrder,
      timestamp: Date.now()
    };

    gameData.calledNumbers.push(calledNumber);
    gameData.lastCallTime = Date.now();

    return calledNumber;
  }

  /**
   * Check for winning patterns
   */
  private checkForWinningPatterns(card: BingoCellData[][]): WinResult {
    // Check full card (only winning condition for now)
    for (let row = 0; row < 5; row++) {
      for (let col = 0; col < 5; col++) {
        const cell = card[row][col];
        if (!cell.isMarked && !cell.isFree) {
          return { hasWon: false };
        }
      }
    }

    // Full card is complete
    return {
      hasWon: true,
      pattern: 'fullCard',
      winningCells: card.flat()
    };
  }

  /**
   * Mark a cell on the bingo card using simplified ID system
   */
  markCell(roomId: string, cellId: string): CellMarkResult {
    const gameData = this.gameData.get(roomId);
    const gameState = this.gameService.getGameState(roomId);

    if (!gameData || !gameState || gameState.status !== 'active') {
      return {
        success: false,
        isCorrect: false,
        points: 0,
        newScore: gameState?.score || 0,
        newLives: gameState?.lives || 0,
        gameEnded: false
      };
    }

    // Find the cell by ID (e.g., "B12", "NFREE", "O65")
    let targetCell: BingoCellData | null = null;
    for (let row = 0; row < 5; row++) {
      for (let col = 0; col < 5; col++) {
        const cell = gameData.bingoCard[row][col];
        if (cell.id === cellId) {
          targetCell = cell;
          break;
        }
      }
      if (targetCell) break;
    }

    if (!targetCell) {
      return {
        success: false,
        isCorrect: false,
        points: 0,
        newScore: gameState.score,
        newLives: gameState.lives,
        gameEnded: false
      };
    }

    // FREE space is always correct to mark
    if (targetCell.isFree) {
      return {
        success: true,
        isCorrect: true,
        points: 0, // No points for FREE space
        newScore: gameState.score,
        newLives: gameState.lives,
        gameEnded: false,
        gameState: this.getBingoGameState(roomId) || undefined
      };
    }

    // Check if the number has been called
    const hasBeenCalled = gameData.calledNumbers.some((called: CalledNumber) =>
      called.column === targetCell!.column && called.number === targetCell!.number
    );

    let points = 0;
    let newLives = gameState.lives;
    let isCorrect = false;

    let matchedCalledNumber: CalledNumber | undefined;

    if (hasBeenCalled && !targetCell.isMarked) {
      // Correct mark
      targetCell.isMarked = true;
      isCorrect = true;

      // Find the specific called number that was matched
      matchedCalledNumber = gameData.calledNumbers.find((called: CalledNumber) =>
        called.column === targetCell!.column && called.number === targetCell!.number
      );

      // Calculate timing-based score for this specific called number
      // Each number has its own 8.5-second timer starting from when it was called
      // Faster clicks = higher score (100 max), slower clicks = lower score (10 min)
      if (matchedCalledNumber) {
        const currentTime = Date.now();
        const timeSinceCall = currentTime - matchedCalledNumber.timestamp;
        points = this.calculateTimingBasedScore(timeSinceCall);
      }
    } else if (!hasBeenCalled) {
      // Wrong mark - deduct life
      newLives = Math.max(0, gameState.lives - 1);
      this.gameService.updateLives(roomId, newLives);
    }

    // Update score if points were earned
    if (points > 0) {
      this.gameService.updateScore(roomId, points, 'add');
    }

    // Check for winning patterns
    const winResult = this.checkForWinningPatterns(gameData.bingoCard);
    let gameEnded = false;

    if (winResult.hasWon) {
      // Add pattern bonus
      const patternBonus = BINGO_CONSTANTS.SCORING.PATTERN_BONUS[winResult.pattern!];
      this.gameService.updateScore(roomId, patternBonus, 'add');
      this.gameService.endGame(roomId, 'completed');
      gameEnded = true;
    } else if (newLives === 0) {
      this.gameService.endGame(roomId, 'no_lives');
      gameEnded = true;
    }

    // Get updated game state for final score
    const updatedGameState = this.gameService.getGameState(roomId);
    const finalScore = updatedGameState?.score || gameState.score;

    return {
      success: true,
      isCorrect,
      points: points + (winResult.hasWon ? BINGO_CONSTANTS.SCORING.PATTERN_BONUS[winResult.pattern!] : 0),
      newScore: finalScore,
      newLives,
      gameEnded,
      winResult: winResult.hasWon ? winResult : undefined,
      gameState: this.getBingoGameState(roomId) || undefined,
      matchedCalledNumber: matchedCalledNumber
    };
  }

  /**
   * Calculate timing-based score for a specific called number
   * Each called number has its own timer that starts when the number is called
   * Matches the client-side logic from RightNumber.getScore()
   */
  private calculateTimingBasedScore(timeSinceCall: number): number {
    // Convert elapsed time to timer value (starts at 1.0, decreases to 0.0 over 8.5 seconds)
    const timerDuration = BINGO_CONSTANTS.SCORING.TIMER_DURATION; // 8500ms
    const timerValue = Math.max(0, 1 - (timeSinceCall / timerDuration));

    // If clicked immediately (timer still at 1.0), give max score
    if (timerValue >= 1) return BINGO_CONSTANTS.SCORING.MAX_SCORE;

    // Calculate score based on remaining timer value
    // Score decreases from 100 to 10 as timer goes from 1.0 to 0.0
    const score = BINGO_CONSTANTS.SCORING.MAX_SCORE - Math.floor((1 - timerValue) * 90);

    // Ensure minimum score of 10, even if timer has expired
    return Math.max(BINGO_CONSTANTS.SCORING.MIN_SCORE, score);
  }

  /**
   * Get current bingo game state for client synchronization
   */
  getBingoGameState(roomId: string): BingoGameState | null {
    const gameData = this.gameData.get(roomId);
    if (!gameData) {
      return null;
    }

    return {
      bingoCard: gameData.bingoCard,
      calledNumbers: gameData.calledNumbers,
      currentCallOrder: gameData.currentCallOrder,
      gameStartTime: gameData.gameStartTime,
      lastCallTime: gameData.lastCallTime,
      isGameActive: gameData.isGameActive
    };
  }

  /**
   * Initialize a new bingo game with PRNG-based randomization
   */
  initializeBingoGame(roomId: string): boolean {
    // Generate seeds for PRNG
    const serverSeed = `bingo-server-${Date.now()}-${Math.random()}`;
    const clientSeed = `bingo-client-${roomId}-${Date.now()}`;
    const nonce = 0;

    const { card, availableNumbers } = this.generateBingoCard(serverSeed, clientSeed, nonce);

    const gameData: BingoGameData = {
      roomId,
      bingoCard: card,
      calledNumbers: [],
      availableNumbers,
      currentCallOrder: 0,
      gameStartTime: Date.now(),
      lastCallTime: 0,
      serverSeed,
      clientSeed,
      nonce,
      isGameActive: true
    };

    this.gameData.set(roomId, gameData);
    logger.info(`Bingo game data initialized for room ${roomId} with serverSeed ${serverSeed}`);
    return true;
  }

  /**
   * Call next number and return result
   */
  callNextBingoNumber(roomId: string): NumberCallResult | null {
    const calledNumber = this.callNextNumber(roomId);
    if (!calledNumber) {
      return null;
    }

    const gameState = this.getBingoGameState(roomId);
    if (!gameState) {
      return null;
    }

    return {
      calledNumber,
      gameState
    };
  }

  /**
   * Initialize and start a new Bingo game session
   */
  initializeAndStartGame(roomId: string, socket: Socket): GameInitResult {
    // Check if room already has a game state
    let gameState = this.gameService.getGameState(roomId);

    if (gameState) {
      // Game state exists, check if we can start
      if (gameState.status === 'active') {
        return { success: false, message: 'Game is already active' };
      }
      if (gameState.status === 'ended') {
        return { success: false, message: 'Game session has ended - no restarts allowed' };
      }
    } else {
      // Create new game state with 3 lives for Bingo
      gameState = this.gameService.createGameState(roomId, GAME_TYPES.BINGO, 3);
    }

    // Initialize bingo-specific game data
    const bingoInitSuccess = this.initializeBingoGame(roomId);
    if (!bingoInitSuccess) {
      return { success: false, message: 'Failed to initialize bingo game data' };
    }

    // Start the game using GameService
    const startSuccess = this.gameService.startGame(roomId, socket);
    if (!startSuccess) {
      return { success: false, message: 'Failed to start game' };
    }

    logger.info(`Bingo game initialized and started for room ${roomId}`);

    return { success: true, gameState };
  }

  /**
   * Start automatic number calling for a room
   */
  startAutomaticNumberCalling(roomId: string, socket: Socket): void {
    // Clear any existing timer
    this.stopAutomaticNumberCalling(roomId);

    // Start with initial delay
    const initialTimer = setTimeout(() => {
      this.callAndBroadcastNumber(roomId, socket);

      // Set up recurring timer
      const recurringTimer = setInterval(() => {
        this.callAndBroadcastNumber(roomId, socket);
      }, BINGO_CONSTANTS.CALLOUT_DELAY);

      this.numberCallTimers.set(roomId, recurringTimer);
    }, BINGO_CONSTANTS.INITIAL_CALLOUT_DELAY);

    this.numberCallTimers.set(roomId, initialTimer);
  }

  /**
   * Stop automatic number calling for a room
   */
  stopAutomaticNumberCalling(roomId: string): void {
    const timer = this.numberCallTimers.get(roomId);
    if (timer) {
      clearTimeout(timer);
      clearInterval(timer);
      this.numberCallTimers.delete(roomId);
    }
  }

  /**
   * Call next number and broadcast to all players in room
   */
  private callAndBroadcastNumber(roomId: string, socket: Socket): void {
    const gameState = this.gameService.getGameState(roomId);
    if (!gameState || gameState.status !== 'active') {
      this.stopAutomaticNumberCalling(roomId);
      return;
    }

    const result = this.callNextBingoNumber(roomId);
    if (result) {
      // Broadcast to all players in the room
      socket.emit('number_called', {
        calledNumber: result.calledNumber,
        gameState: result.gameState
      });

      logger.info(`Called number ${result.calledNumber.column}${result.calledNumber.number} in room ${roomId}`);
    } else {
      // No more numbers to call - end game
      this.stopAutomaticNumberCalling(roomId);
      this.endGame(roomId, 'completed');

      socket.emit('ended', {
        reason: 'completed',
        finalScore: gameState.score
      });
    }
  }

  /**
   * Clean up game data when game ends
   */
  cleanupGame(roomId: string): void {
    // Stop automatic number calling
    this.stopAutomaticNumberCalling(roomId);

    // Clean up game data
    this.gameData.delete(roomId);
    logger.info(`Cleaned up bingo game data for room ${roomId}`);
  }

  /**
   * End the game session
   */
  endGame(roomId: string, reason: EndReason): boolean {
    const gameState = this.gameService.getGameState(roomId);
    if (!gameState || gameState.status !== 'active') {
      logger.warn(`Cannot end game: no active game in room ${roomId}`);
      return false;
    }

    // End game using GameService
    this.gameService.endGame(roomId, reason);

    // Clean up bingo-specific game data
    this.cleanupGame(roomId);

    logger.info(`Bingo game ended in room ${roomId}, reason: ${reason}`);
    return true;
  }

  /**
   * Handle generic game start event
   */
  handleGameStart(io: Server, socket: Socket, data: GameStartData): void {
    const { roomId, gameId } = data;

    if (!roomId || !gameId) {
      socket.emit('error', {
        message: 'Missing roomId or gameId'
      });
      return;
    }

    try {
      const result = this.initializeAndStartGame(roomId, socket);

      if (result.success) {
        // Get initial game state for client
        const bingoGameState = this.getBingoGameState(roomId);

        // Start automatic number calling
        this.startAutomaticNumberCalling(roomId, socket);

        // Broadcast game start to all players in room
       socket.emit('started', {
          gameId,
          gameState: result.gameState,
          bingoGameState,
          message: 'Bingo game started!'
        });

        logger.info(`${gameId} game started in room ${roomId}`);
      } else {
        socket.emit('error', {
          message: result.message || 'Failed to start game'
        });
      }
    } catch (error) {
      logger.error(`Error starting ${gameId} game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle game action events (cell marking, etc.)
   */
  handleGameAction(io: Server, socket: Socket, data: GameActionData): void {
    const { roomId, gameId, action } = data;

    if (!roomId || !gameId || !action) {
      socket.emit('error', {
        message: 'Missing roomId, gameId, or action'
      });
      return;
    }

    try {
      // Handle specific action types for Bingo
      switch (action.type) {
        case 'cell_mark':
          this.handleCellMarkAction(socket, data);
          break;
        case 'call_number':
          this.handleCallNumberAction(io, socket, data);
          break;
        default:
          socket.emit('error', {
            message: `Unknown action type: ${action.type}`
          });
      }
    } catch (error) {
      logger.error(`Error processing game action in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle cell marking action using simplified ID system
   */
  private handleCellMarkAction(socket: Socket, data: GameActionData): void {
    const { roomId, action } = data;
    const { cellId } = action.data;

    if (!cellId) {
      socket.emit('error', {
        message: 'Missing cellId in action data'
      });
      return;
    }

    const result = this.markCell(roomId, cellId);

    // Send result back to client
    socket.emit('action_result', {
      actionType: 'cell_mark',
      data: {
        cellId,
        isCorrect: result.isCorrect,
        points: result.points,
        newScore: result.newScore,
        newLives: result.newLives,
        gameEnded: result.gameEnded,
        winResult: result.winResult,
        gameState: result.gameState,
        matchedCalledNumber: result.matchedCalledNumber
      }
    });

    // If game ended, broadcast game end event
    if (result.gameEnded) {
      socket.emit('ended', {
        reason: result.winResult?.hasWon ? 'completed' : 'no_lives',
        finalScore: result.newScore
      });
    }
  }

  /**
   * Handle number calling action (for manual number calling)
   */
  private handleCallNumberAction(io: Server, socket: Socket, data: GameActionData): void {
    const { roomId } = data;

    const result = this.callNextBingoNumber(roomId);

    if (result) {
      // Broadcast new number to all players in room
      io.to(roomId).emit('number_called', {
        calledNumber: result.calledNumber,
        gameState: result.gameState
      });
    } else {
      // No more numbers to call
      socket.emit('error', {
        message: 'No more numbers available to call'
      });
    }
  }

  /**
   * Handle generic game end event
   */
  handleGameEnd(socket: Socket, data: GameEndData): void {
    const { roomId, reason = 'manual', gameId } = data;

    if (!roomId  || !gameId) {
      socket.emit('error', {
        message: 'Missing roomId, or gameId'
      });
      return;
    }

    try {
      const gameState = this.gameService.getGameState(roomId);
      if (!gameState) {
        socket.emit('error', {
          message: 'Game state not found'
        });
        return;
      }

      const success = this.endGame(roomId, reason);

      if (success) {
        // Broadcast game end to all players in room
        socket.emit('ended', {
          reason,
          finalScore: gameState.score,
        });

        logger.info(`${gameId} game ended in room ${roomId}, reason: ${reason}`);
      } else {
        socket.emit('error', {
          message: 'Failed to end game'
        });
      }
    } catch (error) {
      logger.error(`Error ending ${gameId} game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Setup socket event handlers for Bingo
   */
  public setupSocketHandlers(io: Server, socket: Socket): void {
    // Generic game start event
    socket.on('start', (data) => {
      console.log('Received start event:', data);
      
      if (data.gameId === GAME_TYPES.BINGO) {
        this.handleGameStart(io, socket, data);
      }
    });

    // Generic game end event
    socket.on('end', (data) => {
      if (data.gameId === GAME_TYPES.BINGO) {
        this.handleGameEnd(socket, data);
      }
    });

    // Generic game action event (for game-specific actions)
    socket.on('action', (data) => {
      if (data.gameId === GAME_TYPES.BINGO) {
        this.handleGameAction(io, socket, data);
      }
    });
  }
}