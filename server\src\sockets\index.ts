import { Server, Socket } from 'socket.io';
import { FingerFrenzy<PERSON><PERSON>roller, BingoController, MatchingMayhemController } from '../controllers';
import gameService from '../services/gameService';

// Initialize game controllers
const fingerFrenzyController = new FingerFrenzyController(gameService);
const bingoController = new BingoController(gameService);
const matchingMayhemController = new MatchingMayhemController(gameService);

export function setupSocketHandlers(io: Server) {
  io.on('connection', (socket: Socket) => {
    console.log(`Client connected: ${socket.id}`);

    // Setup game-specific handlers
    fingerFrenzyController.setupSocketHandlers(io, socket);
    bingoController.setupSocketHandlers(io, socket);
    matchingMayhemController.setupSocketHandlers(io, socket);

    // Handle room joining
    socket.on('join_room', (data) => {
      const { roomId, playerId, gameType } = data;
      console.log(`Player ${playerId} joining room ${roomId} for game ${gameType}`);
      
      socket.join(roomId);
      socket.emit('room_joined', { 
        roomId, 
        playerId, 
        gameType,
        message: 'Successfully joined room' 
      });
      
      // Notify other players in the room
      socket.to(roomId).emit('player_joined', { playerId, gameType });
    });

    // Handle room leaving
    socket.on('leave_room', (data) => {
      const { roomId, playerId } = data;
      console.log(`Player ${playerId} leaving room ${roomId}`);
      
      socket.leave(roomId);
      socket.emit('room_left', { roomId, playerId });
      
      // Notify other players in the room
      socket.to(roomId).emit('player_left', { playerId });
    });

    // Handle player ready status
    socket.on('player_ready', (data) => {
      const { roomId, playerId, ready } = data;
      console.log(`Player ${playerId} ready status: ${ready} in room ${roomId}`);
      
      // Broadcast to room
      socket.to(roomId).emit('player_ready_update', { playerId, ready });
    });

    // Handle game actions
    socket.on('game_action', (data) => {
      const { roomId, playerId, action, gameData } = data;
      console.log(`Game action from ${playerId} in room ${roomId}:`, action);
      
      // Broadcast action to other players in the room
      socket.to(roomId).emit('opponent_action', { 
        playerId, 
        action, 
        gameData,
        timestamp: Date.now()
      });
    });

    // Handle score submission
    socket.on('submit_score', (data) => {
      const { roomId, playerId, score, gameType } = data;
      console.log(`Score submission from ${playerId}: ${score} in room ${roomId}`);
      
      // TODO: Validate and submit score to Python backend
      
      // Broadcast score update to room
      socket.to(roomId).emit('score_update', { 
        playerId, 
        score, 
        gameType,
        timestamp: Date.now()
      });
    });

    // Handle game start
    socket.on('start_game', (data) => {
      const { roomId, gameType } = data;
      console.log(`Starting game ${gameType} in room ${roomId}`);
      
      // Broadcast game start to all players in room
      io.to(roomId).emit('game_start', { 
        gameType, 
        startTime: Date.now(),
        message: 'Game has started!'
      });
    });

    // Handle game end
    socket.on('end_game', (data) => {
      const { roomId, gameType, results } = data;
      console.log(`Ending game ${gameType} in room ${roomId}`);
      
      // Broadcast game end to all players in room
      io.to(roomId).emit('game_end', { 
        gameType, 
        results,
        endTime: Date.now(),
        message: 'Game has ended!'
      });
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      console.log(`Client disconnected: ${socket.id}`);
      // TODO: Handle cleanup of player from rooms
    });

    // Basic message echo for testing
    socket.on('message', (data) => {
      console.log(`Received message: ${data}`);
      socket.emit('message', `Echo: ${data}`);
    });
  });
}
